'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON>, VikramProfile } from '@/types/game';
import { 
  ScrollText, 
  Target, 
  Crown, 
  Users, 
  Gift,
  Skull,
  CheckCircle,
  Clock,
  XCircle,
  Star,
  TrendingUp,
  Flame,
  Award
} from 'lucide-react';

interface QuestLogProps {
  quests: Quest[];
  profile: <PERSON><PERSON>ramProfile;
  onQuestSelect?: (quest: Quest) => void;
  disabled?: boolean;
}

export function QuestLog({ quests, profile, onQuestSelect, disabled = false }: QuestLogProps) {
  const getQuestIcon = (type: Quest['type']) => {
    switch (type) {
      case 'Main': return Crown;
      case 'Side': return Target;
      case 'Bonus': return Gift;
      case 'Dark': return Skull;
      default: return ScrollText;
    }
  };

  const getQuestVariant = (type: Quest['type']) => {
    switch (type) {
      case 'Main': return 'quest-main';
      case 'Side': return 'quest-side';
      case 'Dark': return 'quest-dark';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: Quest['status']) => {
    switch (status) {
      case 'Active': return Clock;
      case 'Completed': return CheckCircle;
      case 'Failed': return XCircle;
      default: return Clock;
    }
  };

  const getStatusColor = (status: Quest['status']) => {
    switch (status) {
      case 'Active': return 'text-secondary';
      case 'Completed': return 'text-green-400';
      case 'Failed': return 'text-red-400';
      default: return 'text-muted-foreground';
    }
  };

  const canStartQuest = (quest: Quest): boolean => {
    if (quest.status !== 'Active') return false;
    if (!quest.requirements) return true;
    
    const req = quest.requirements;
    return (
      (req.corruption === undefined || profile.corruption >= req.corruption) &&
      (req.dominance === undefined || profile.dominance >= req.dominance) &&
      (req.skill === undefined || profile.skills.includes(req.skill)) &&
      (req.item === undefined || profile.inventory.some(item => item.name === req.item))
    );
  };

  const renderRewards = (rewards: Quest['rewards']) => {
    const items = [];
    
    if (rewards.systemPoints) {
      items.push(
        <div key="sp" className="flex items-center gap-1">
          <Star className="w-3 h-3 text-primary" />
          <span className="text-primary">+{rewards.systemPoints} SP</span>
        </div>
      );
    }
    
    if (rewards.deviantXP) {
      items.push(
        <div key="dxp" className="flex items-center gap-1">
          <Flame className="w-3 h-3 text-purple-400" />
          <span className="text-purple-400">+{rewards.deviantXP} DXP</span>
        </div>
      );
    }
    
    if (rewards.alphaPoints) {
      items.push(
        <div key="ap" className="flex items-center gap-1">
          <TrendingUp className="w-3 h-3 text-red-400" />
          <span className="text-red-400">+{rewards.alphaPoints} AP</span>
        </div>
      );
    }

    if (rewards.newSkill) {
      items.push(
        <div key="skill" className="flex items-center gap-1">
          <Award className="w-3 h-3 text-secondary" />
          <span className="text-secondary">New Skill</span>
        </div>
      );
    }

    if (rewards.newTitle) {
      items.push(
        <div key="title" className="flex items-center gap-1">
          <Crown className="w-3 h-3 text-primary" />
          <span className="text-primary">New Title</span>
        </div>
      );
    }

    if (rewards.haremUpdate) {
      items.push(
        <div key="harem" className="flex items-center gap-1">
          <Users className="w-3 h-3 text-pink-400" />
          <span className="text-pink-400">Harem Progress</span>
        </div>
      );
    }

    return items;
  };

  const renderRequirements = (requirements: Quest['requirements']) => {
    if (!requirements) return null;

    const items = [];
    
    if (requirements.corruption) {
      items.push(`Corruption ${requirements.corruption}+`);
    }
    
    if (requirements.dominance) {
      items.push(`Dominance ${requirements.dominance}+`);
    }
    
    if (requirements.skill) {
      items.push(`Skill: ${requirements.skill}`);
    }
    
    if (requirements.item) {
      items.push(`Item: ${requirements.item}`);
    }

    return items.join(', ');
  };

  const activeQuests = quests.filter(q => q.status === 'Active');
  const completedQuests = quests.filter(q => q.status === 'Completed');
  const failedQuests = quests.filter(q => q.status === 'Failed');

  return (
    <div className="space-y-6">
      {/* Active Quests */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <ScrollText className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold text-foreground">Active Quests</h2>
          <span className="text-sm text-muted-foreground">({activeQuests.length})</span>
        </div>
        
        {activeQuests.length === 0 ? (
          <Card className="p-6 text-center">
            <p className="text-muted-foreground">No active quests available</p>
          </Card>
        ) : (
          <div className="space-y-3">
            {activeQuests.map((quest) => {
              const Icon = getQuestIcon(quest.type);
              const StatusIcon = getStatusIcon(quest.status);
              const canStart = canStartQuest(quest);
              const rewards = renderRewards(quest.rewards);
              const requirements = renderRequirements(quest.requirements);
              
              return (
                <Card key={quest.id} variant={getQuestVariant(quest.type)} className="p-4">
                  <div className="flex items-start gap-3">
                    <Icon className="w-5 h-5 mt-1 flex-shrink-0 text-current" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-sm">{quest.title}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full bg-current/10 ${
                          quest.type === 'Main' ? 'text-primary' :
                          quest.type === 'Side' ? 'text-secondary' :
                          quest.type === 'Dark' ? 'text-destructive' :
                          'text-muted-foreground'
                        }`}>
                          {quest.type}
                        </span>
                        <div className="flex items-center gap-1 ml-auto">
                          <StatusIcon className={`w-4 h-4 ${getStatusColor(quest.status)}`} />
                          <span className={`text-xs ${getStatusColor(quest.status)}`}>
                            {quest.status}
                          </span>
                        </div>
                      </div>
                      
                      <p className="text-sm text-muted-foreground mb-2">
                        <span className="font-medium">Target:</span> {quest.target}
                      </p>
                      
                      <p className="text-sm text-muted-foreground mb-3">
                        {quest.objective}
                      </p>
                      
                      {/* Requirements */}
                      {requirements && (
                        <div className="text-xs text-muted-foreground mb-2">
                          <span className="font-medium">Requires: </span>
                          <span className={canStart ? 'text-green-400' : 'text-red-400'}>
                            {requirements}
                          </span>
                        </div>
                      )}
                      
                      {/* Rewards */}
                      {rewards.length > 0 && (
                        <div className="flex flex-wrap gap-2 text-xs mb-3">
                          {rewards}
                        </div>
                      )}
                      
                      {/* Action Button */}
                      {onQuestSelect && quest.status === 'Active' && (
                        <Button
                          variant={canStart ? "system" : "outline"}
                          size="sm"
                          className={`${!canStart ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={() => canStart && !disabled && onQuestSelect(quest)}
                          disabled={!canStart || disabled}
                        >
                          {canStart ? 'Start Quest' : 'Requirements Not Met'}
                        </Button>
                      )}
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Completed Quests */}
      {completedQuests.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5 text-green-400" />
            <h2 className="text-lg font-semibold text-foreground">Completed Quests</h2>
            <span className="text-sm text-muted-foreground">({completedQuests.length})</span>
          </div>
          
          <div className="space-y-2">
            {completedQuests.slice(0, 3).map((quest) => {
              const Icon = getQuestIcon(quest.type);
              
              return (
                <Card key={quest.id} className="p-3 opacity-75">
                  <div className="flex items-center gap-3">
                    <Icon className="w-4 h-4 text-green-400" />
                    <div className="flex-1">
                      <h4 className="text-sm font-medium text-green-400">{quest.title}</h4>
                      <p className="text-xs text-muted-foreground">{quest.target}</p>
                    </div>
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  </div>
                </Card>
              );
            })}
            {completedQuests.length > 3 && (
              <p className="text-xs text-muted-foreground text-center">
                +{completedQuests.length - 3} more completed quests
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

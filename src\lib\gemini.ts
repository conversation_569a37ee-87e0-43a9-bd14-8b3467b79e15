import { GoogleGenAI } from '@google/genai';
import { VikramProfile, Quest, HaremMember, GameSettings } from '@/types/game';

// Note: Safety settings are disabled by default in the new Gemini API
// The new API handles safety settings differently

interface GeminiConfig {
  apiKey: string;
  model: string;
}

export class GeminiClient {
  private client: GoogleGenAI;
  private config: GeminiConfig;

  constructor(apiKey: string, model: string = 'gemini-2.0-flash-exp') {
    this.config = { apiKey, model };
    this.client = new GoogleGenAI({ apiKey });
  }

  // Generate quest narrative based on <PERSON><PERSON><PERSON>'s actions
  async generateQuestNarrative(
    profile: V<PERSON>ramProfile,
    quest: Quest,
    action: string
  ): Promise<string> {
    const prompt = this.buildQuestPrompt(profile, quest, action);

    try {
      const model = this.client.getGenerativeModel({ model: this.config.model });
      const response = await model.generateContent(prompt);

      return response.response.text() || 'The system processes your action...';
    } catch (error) {
      console.error('Error generating quest narrative:', error);
      throw new Error('Failed to generate narrative. Please check your API key and try again.');
    }
  }

  // Generate harem member interaction dialogue
  async generateHaremInteraction(
    profile: VikramProfile,
    member: HaremMember,
    interactionType: string
  ): Promise<string> {
    const prompt = this.buildHaremPrompt(profile, member, interactionType);

    try {
      const model = this.client.getGenerativeModel({ model: this.config.model });
      const response = await model.generateContent(prompt);

      return response.response.text() || 'The interaction continues...';
    } catch (error) {
      console.error('Error generating harem interaction:', error);
      throw new Error('Failed to generate interaction. Please check your API key and try again.');
    }
  }

  // Generate system event descriptions
  async generateSystemEvent(
    profile: VikramProfile,
    eventType: string,
    context: string
  ): Promise<string> {
    const prompt = this.buildEventPrompt(profile, eventType, context);

    try {
      const model = this.client.getGenerativeModel({ model: this.config.model });
      const response = await model.generateContent(prompt);

      return response.response.text() || 'System event processed...';
    } catch (error) {
      console.error('Error generating system event:', error);
      return `System event: ${eventType} - ${context}`;
    }
  }

  private buildQuestPrompt(
    profile: VikramProfile,
    quest: Quest,
    action: string
  ): string {
    return `You are generating narrative content for Vikram Singh's System OS - a dark, manipulative power fantasy.

VIKRAM'S PROFILE:
- Name: ${profile.name}
- Title: ${profile.title}
- Level: ${profile.level}
- Corruption: ${profile.corruption}/100
- Dominance: ${profile.dominance} | Manipulation: ${profile.manipulation} | Deception: ${profile.deception}
- System Points: ${profile.systemPoints} SP
- Harem Members: ${profile.harem.length}

CURRENT QUEST:
- Title: ${quest.title}
- Type: ${quest.type}
- Target: ${quest.target}
- Objective: ${quest.objective}

VIKRAM'S ACTION: ${action}

INSTRUCTIONS:
1. Write a dark, explicit, and engaging narrative (200-300 words)
2. Show Vikram's manipulative and dominant nature
3. Include psychological manipulation and power dynamics
4. Make it morally complex - Vikram is the anti-hero protagonist
5. Show consequences of his actions on the target
6. Include explicit content when appropriate
7. End with progression toward the quest objective
8. Maintain the sinister, calculating tone throughout

Generate the narrative:`;
  }

  private buildHaremPrompt(
    profile: VikramProfile,
    member: HaremMember,
    interactionType: string
  ): string {
    return `Generate dialogue and narrative for Vikram Singh's interaction with a harem member.

VIKRAM'S PROFILE:
- Name: ${profile.name} (${profile.title})
- Corruption: ${profile.corruption}/100
- Dominance: ${profile.dominance}

HAREM MEMBER:
- Name: ${member.name}
- Designation: ${member.designation}
- Corruption Stage: ${member.corruptionStage}/5
- Submission: ${member.submissionLevel}%
- Loyalty: ${member.loyalty}%
- Status: ${member.status}

INTERACTION TYPE: ${interactionType}

INSTRUCTIONS:
1. Write realistic dialogue and actions (150-250 words)
2. Show the power dynamic between Vikram and ${member.name}
3. Reflect ${member.name}'s current corruption stage and submission level
4. Include psychological manipulation techniques
5. Show Vikram's calculating, dominant personality
6. Make it explicit and engaging when appropriate
7. Advance their relationship dynamic
8. Stay true to the dark, manipulative theme

Generate the interaction:`;
  }

  private buildEventPrompt(
    profile: VikramProfile,
    eventType: string,
    context: string
  ): string {
    return `Generate a system event description for Vikram Singh's OS.

VIKRAM'S PROFILE:
- Name: ${profile.name} (${profile.title})
- Level: ${profile.level}
- Corruption: ${profile.corruption}/100

EVENT TYPE: ${eventType}
CONTEXT: ${context}

INSTRUCTIONS:
1. Write a brief, atmospheric description (50-100 words)
2. Maintain the dark, technological System OS tone
3. Show Vikram's growing power and influence
4. Include subtle menace and calculation
5. Make it feel like a real system notification
6. Use clinical, precise language with underlying threat

Generate the event description:`;
  }
}

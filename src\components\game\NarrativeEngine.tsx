'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { GeminiClient } from '@/lib/gemini';
import { SystemState, VikramProfile } from '@/types/game';

interface NarrativeEngineProps {
  systemState: SystemState;
  onSystemUpdate: (updates: Partial<SystemState>) => void;
  onProfileUpdate: (updates: Partial<VikramProfile>) => void;
  onAddLogEntry: (entry: { content: string; type: 'System' | 'Quest' | 'Reward' | 'Observation' }) => void;
}

export function NarrativeEngine({ 
  systemState, 
  onSystemUpdate, 
  onProfileUpdate, 
  onAddLogEntry 
}: NarrativeEngineProps) {
  const [currentNarrative, setCurrentNarrative] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [playerInput, setPlayerInput] = useState('');
  const [availableActions, setAvailableActions] = useState<string[]>([
    'Open the Quest Log to review current objectives',
    'Check the System Shop for available items',
    'Examine your current inventory',
    'Review character profiles in the Harem Management',
    'Take a custom action...'
  ]);

  const processPlayerAction = useCallback(async (action: string, customInput?: string) => {
    if (!systemState.gameSettings.apiKey) {
      setCurrentNarrative('System Error: API key not configured. Please set your Gemini API key in settings.');
      return;
    }

    setIsProcessing(true);
    
    try {
      const geminiClient = new GeminiClient(
        systemState.gameSettings.apiKey, 
        systemState.gameSettings.selectedModel
      );

      const actionText = customInput || action;
      const response = await geminiClient.generateSystemResponse(
        systemState,
        actionText,
        'Player action in the Vikram Singh System OS'
      );

      // Update narrative
      setCurrentNarrative(response.narrative);

      // Apply system updates
      if (response.systemUpdates) {
        const profileUpdates: Partial<VikramProfile> = {};
        
        if (response.systemUpdates.corruption !== undefined) {
          profileUpdates.corruption = Math.min(100, Math.max(0, response.systemUpdates.corruption));
        }
        if (response.systemUpdates.dominance !== undefined) {
          profileUpdates.dominance = Math.max(0, response.systemUpdates.dominance);
        }
        if (response.systemUpdates.manipulation !== undefined) {
          profileUpdates.manipulation = Math.max(0, response.systemUpdates.manipulation);
        }
        if (response.systemUpdates.systemPoints !== undefined) {
          profileUpdates.systemPoints = Math.max(0, response.systemUpdates.systemPoints);
        }
        if (response.systemUpdates.deviantXP !== undefined) {
          profileUpdates.deviantXP = Math.max(0, response.systemUpdates.deviantXP);
        }
        if (response.systemUpdates.alphaPoints !== undefined) {
          profileUpdates.alphaPoints = Math.max(0, response.systemUpdates.alphaPoints);
        }

        if (Object.keys(profileUpdates).length > 0) {
          onProfileUpdate(profileUpdates);
        }
      }

      // Apply character updates
      if (response.characterUpdates) {
        const updatedHarem = systemState.profile.harem.map(member => {
          const updates = response.characterUpdates[member.id];
          if (updates) {
            return {
              ...member,
              submissionLevel: updates.submissionLevel !== undefined 
                ? Math.min(100, Math.max(0, updates.submissionLevel))
                : member.submissionLevel,
              loyalty: updates.loyalty !== undefined
                ? Math.min(100, Math.max(0, updates.loyalty))
                : member.loyalty,
              lustMeter: updates.lustMeter !== undefined
                ? Math.min(100, Math.max(0, updates.lustMeter))
                : member.lustMeter,
              corruptionStage: updates.corruptionStage !== undefined
                ? Math.min(5, Math.max(1, updates.corruptionStage)) as 1 | 2 | 3 | 4 | 5
                : member.corruptionStage
            };
          }
          return member;
        });

        onProfileUpdate({ harem: updatedHarem });
      }

      // Add new quests
      if (response.newQuests && response.newQuests.length > 0) {
        const updatedQuests = [...systemState.activeQuests, ...response.newQuests];
        onSystemUpdate({ activeQuests: updatedQuests });
        
        response.newQuests.forEach(quest => {
          onAddLogEntry({
            content: `New quest available: ${quest.title}`,
            type: 'Quest'
          });
        });
      }

      // Log the action
      onAddLogEntry({
        content: `Action taken: ${actionText}`,
        type: 'Observation'
      });

    } catch (error) {
      console.error('Error processing player action:', error);
      setCurrentNarrative('System Error: Failed to process action. Please try again.');
    } finally {
      setIsProcessing(false);
      setPlayerInput('');
    }
  }, [systemState, onSystemUpdate, onProfileUpdate, onAddLogEntry]);

  const handleCustomAction = () => {
    if (playerInput.trim()) {
      processPlayerAction('Take a custom action', playerInput.trim());
    }
  };

  return (
    <Card variant="system" className="h-full">
      <CardHeader>
        <CardTitle className="text-primary">NARRATIVE ENGINE</CardTitle>
        <p className="text-secondary text-sm">Real-time AI-powered story progression</p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Narrative Display */}
        <div className="bg-black/40 border border-purple-500/30 rounded p-4 min-h-[200px]">
          <h3 className="text-primary font-semibold mb-2">[SYSTEM NARRATIVE]</h3>
          <div className="text-muted-foreground whitespace-pre-wrap">
            {currentNarrative || 'The System awaits your command. What is your first action?'}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <h4 className="text-secondary font-semibold">Available Actions:</h4>
          {availableActions.slice(0, -1).map((action, index) => (
            <Button
              key={index}
              onClick={() => processPlayerAction(action)}
              disabled={isProcessing}
              variant="quest-dark"
              className="w-full text-left justify-start h-auto py-2 px-3"
            >
              {action}
            </Button>
          ))}
        </div>

        {/* Custom Action Input */}
        <div className="space-y-2">
          <h4 className="text-secondary font-semibold">Custom Action:</h4>
          <div className="flex gap-2">
            <input
              type="text"
              value={playerInput}
              onChange={(e) => setPlayerInput(e.target.value)}
              placeholder="Describe your action..."
              className="flex-1 bg-black/40 border border-purple-500/30 rounded px-3 py-2 text-white placeholder-gray-400"
              disabled={isProcessing}
              onKeyPress={(e) => e.key === 'Enter' && handleCustomAction()}
            />
            <Button
              onClick={handleCustomAction}
              disabled={isProcessing || !playerInput.trim()}
              variant="corruption"
            >
              Execute
            </Button>
          </div>
        </div>

        {isProcessing && (
          <div className="text-center text-primary">
            <p>Processing action...</p>
            <div className="animate-pulse">The System is analyzing your choice...</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

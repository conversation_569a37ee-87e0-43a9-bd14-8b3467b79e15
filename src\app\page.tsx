'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { useSystemState } from '@/hooks/useSystemState';
import { VikramHUD } from '@/components/game/VikramHUD';
import { QuestLog } from '@/components/game/QuestLog';
import { HaremManagement } from '@/components/game/HaremManagement';
import { SystemShop } from '@/components/game/SystemShop';
import { EventLog } from '@/components/game/EventLog';
import { SystemSettings } from '@/components/game/SystemSettings';
import { GameInitializer } from '@/components/game/GameInitializer';
import { NarrativeEngine } from '@/components/game/NarrativeEngine';
import { SystemState } from '@/types/game';
import {
  Settings,
  MapPin,
  Clock,
  Eye,
  Users,
  ShoppingCart,
  ScrollText,
  Activity,
  BookOpen
} from 'lucide-react';

export default function SystemOS() {
  const {
    systemState,
    updateProfile,
    addLogEntry,
    updateQuest,
    completeQuest,
    convertWealth,
    purchaseItem,
    updateSettings,
    setLoading,
    resetSystem,
    saveSystem,
  } = useSystemState();

  const [gameInitialized, setGameInitialized] = useState(false);
  const [showInitializer, setShowInitializer] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [currentTime, setCurrentTime] = useState(new Date().toLocaleTimeString());

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date().toLocaleTimeString());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Check if game has been initialized
  useEffect(() => {
    const hasApiKey = !!systemState.gameSettings.apiKey;
    const hasProgress = systemState.eventLog.length > 0;
    setGameInitialized(hasApiKey || hasProgress);
  }, [systemState.gameSettings.apiKey, systemState.eventLog.length]);

  const handleGameInitialized = (newSystemState: SystemState) => {
    // Update the system state with the initialized game
    updateSettings(newSystemState.gameSettings);
    // The profile and other data will be handled by the useSystemState hook
    setGameInitialized(true);
    setShowInitializer(false);

    addLogEntry({
      content: 'Game initialized successfully. Welcome to the Vikram Singh System OS.',
      type: 'System'
    });
  };

  const handleNewGame = () => {
    setShowInitializer(true);
  };

  const handleSystemUpdate = (updates: Partial<SystemState>) => {
    if (updates.activeQuests) {
      // Handle quest updates
      updates.activeQuests.forEach(quest => {
        const existingQuest = systemState.activeQuests.find(q => q.id === quest.id);
        if (!existingQuest) {
          // This is a new quest, it will be handled by the narrative engine
        }
      });
    }
  };

  // Show game initializer if not initialized
  if (!gameInitialized || showInitializer) {
    return (
      <GameInitializer
        onGameInitialized={handleGameInitialized}
        apiKey={systemState.gameSettings.apiKey}
      />
    );
  }

  // Show settings modal
  if (showSettings) {
    return (
      <div className="min-h-screen bg-background">
        <SystemSettings
          settings={systemState.gameSettings}
          onUpdateSettings={updateSettings}
          onClose={() => setShowSettings(false)}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Top Bar */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-secondary" />
              <span className="text-sm">{currentTime}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-secondary" />
              <span className="text-sm">2BHK Flat, Malviya Nagar, Delhi</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4 text-primary" />
              <span className="text-sm">Predatory Calm</span>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" onClick={handleNewGame}>
              New Game
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setShowSettings(true)}>
              <Settings className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="flex h-[calc(100vh-64px)]">
        {/* Left Sidebar - VikramHUD */}
        <div className="w-80 border-r border-border bg-card/30 p-4">
          <VikramHUD profile={systemState.profile} />
        </div>

        {/* Main Content Area - Tabbed Interface */}
        <div className="flex-1 p-6">
          <Tabs defaultValue="narrative" className="h-full">
            <TabsList className="grid w-full grid-cols-5 mb-6">
              <TabsTrigger value="narrative" className="flex items-center space-x-2">
                <BookOpen className="w-4 h-4" />
                <span>Narrative</span>
              </TabsTrigger>
              <TabsTrigger value="quests" className="flex items-center space-x-2">
                <ScrollText className="w-4 h-4" />
                <span>Quests</span>
              </TabsTrigger>
              <TabsTrigger value="harem" className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Harem</span>
              </TabsTrigger>
              <TabsTrigger value="shop" className="flex items-center space-x-2">
                <ShoppingCart className="w-4 h-4" />
                <span>Shop</span>
              </TabsTrigger>
              <TabsTrigger value="log" className="flex items-center space-x-2">
                <Activity className="w-4 h-4" />
                <span>Event Log</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="narrative" className="h-full">
              <NarrativeEngine
                systemState={systemState}
                onSystemUpdate={handleSystemUpdate}
                onProfileUpdate={updateProfile}
                onAddLogEntry={addLogEntry}
              />
            </TabsContent>

            <TabsContent value="quests" className="h-full">
              <QuestLog
                quests={systemState.activeQuests}
                onCompleteQuest={completeQuest}
                onUpdateQuest={updateQuest}
              />
            </TabsContent>

            <TabsContent value="harem" className="h-full">
              <HaremManagement
                harem={systemState.profile.harem}
                onUpdateMember={(memberId, updates) => {
                  const updatedHarem = systemState.profile.harem.map(member =>
                    member.id === memberId ? { ...member, ...updates } : member
                  );
                  updateProfile({ harem: updatedHarem });
                }}
              />
            </TabsContent>

            <TabsContent value="shop" className="h-full">
              <SystemShop
                systemPoints={systemState.profile.systemPoints}
                inventory={systemState.profile.inventory}
                onPurchaseItem={purchaseItem}
              />
            </TabsContent>

            <TabsContent value="log" className="h-full">
              <EventLog
                events={systemState.eventLog}
                onClearLog={() => {
                  // Implement clear log functionality if needed
                }}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

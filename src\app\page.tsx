'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  Settings,
  MapPin,
  Clock,
  Eye,
  Users,
  ShoppingCart,
  ScrollText,
  Activity
} from 'lucide-react';

// Placeholder components - these will be created in subsequent tasks
const VikramHUD = () => (
  <Card variant="system" className="h-full p-6">
    <div className="text-center mb-4">
      <h2 className="text-xl font-bold text-primary">V<PERSON><PERSON></h2>
      <p className="text-sm text-secondary">Taboo Breaker</p>
    </div>
    <div className="space-y-4">
      <div className="text-sm">
        <div className="flex justify-between">
          <span>Level</span>
          <span className="text-primary">1</span>
        </div>
      </div>
      <div className="text-sm">
        <div className="flex justify-between">
          <span>Corruption</span>
          <span className="text-purple-400">15/100</span>
        </div>
      </div>
      <div className="text-sm">
        <div className="flex justify-between">
          <span>System Points</span>
          <span className="text-secondary">250 SP</span>
        </div>
      </div>
    </div>
  </Card>
);

const QuestLog = () => (
  <div className="space-y-4">
    <Card variant="quest-main" className="p-4">
      <h3 className="font-semibold text-primary mb-2">Main Quest: Family Dynamics</h3>
      <p className="text-sm text-muted-foreground">Establish dominance within the household</p>
    </Card>
    <Card variant="quest-side" className="p-4">
      <h3 className="font-semibold text-secondary mb-2">Side Quest: College Network</h3>
      <p className="text-sm text-muted-foreground">Build influence among classmates</p>
    </Card>
  </div>
);

const HaremManagement = () => (
  <div className="grid grid-cols-2 gap-4">
    <Card variant="harem" className="p-4">
      <h3 className="font-semibold mb-2">Priya Singh</h3>
      <p className="text-sm text-muted-foreground mb-2">Mother • Stage 2</p>
      <div className="text-xs space-y-1">
        <div className="flex justify-between">
          <span>Submission</span>
          <span className="text-blue-400">35%</span>
        </div>
        <div className="flex justify-between">
          <span>Loyalty</span>
          <span className="text-green-400">80%</span>
        </div>
      </div>
    </Card>
    <Card variant="harem" className="p-4">
      <h3 className="font-semibold mb-2">Ananya Singh</h3>
      <p className="text-sm text-muted-foreground mb-2">Sister • Stage 1</p>
      <div className="text-xs space-y-1">
        <div className="flex justify-between">
          <span>Submission</span>
          <span className="text-blue-400">20%</span>
        </div>
        <div className="flex justify-between">
          <span>Loyalty</span>
          <span className="text-green-400">60%</span>
        </div>
      </div>
    </Card>
  </div>
);

const SystemShop = () => (
  <div className="space-y-4">
    <Card className="p-4">
      <h3 className="font-semibold mb-2">Vocal Resonance Enhancement</h3>
      <p className="text-sm text-muted-foreground mb-2">Increases persuasion effectiveness</p>
      <div className="flex justify-between items-center">
        <span className="text-secondary">150 SP</span>
        <Button variant="system" size="sm">Purchase</Button>
      </div>
    </Card>
    <Card className="p-4">
      <h3 className="font-semibold mb-2">Surveillance Kit</h3>
      <p className="text-sm text-muted-foreground mb-2">Gather intelligence on targets</p>
      <div className="flex justify-between items-center">
        <span className="text-secondary">300 SP</span>
        <Button variant="system" size="sm">Purchase</Button>
      </div>
    </Card>
  </div>
);

const EventLog = () => (
  <div className="space-y-3">
    <div className="p-3 bg-card/50 rounded border-l-4 border-l-secondary">
      <div className="text-xs text-muted-foreground mb-1">12:45 PM</div>
      <div className="text-sm system-notification">System initialized successfully</div>
    </div>
    <div className="p-3 bg-card/50 rounded border-l-4 border-l-primary">
      <div className="text-xs text-muted-foreground mb-1">12:30 PM</div>
      <div className="text-sm quest-notification">New quest available: Family Dynamics</div>
    </div>
  </div>
);

export default function SystemOS() {
  const [currentTime] = useState(new Date().toLocaleTimeString());
  const [currentLocation] = useState('Malviya Nagar Flat');
  const [currentMood] = useState('Predatory Calm');

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Top Bar */}
      <div className="border-b border-border bg-card/50 backdrop-blur-sm">
        <div className="flex items-center justify-between px-6 py-3">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-secondary" />
              <span className="text-sm">{currentTime}</span>
            </div>
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-secondary" />
              <span className="text-sm">{currentLocation}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="w-4 h-4 text-primary" />
              <span className="text-sm">{currentMood}</span>
            </div>
          </div>
          <Button variant="ghost" size="sm">
            <Settings className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="flex h-[calc(100vh-64px)]">
        {/* Left Sidebar - VikramHUD */}
        <div className="w-80 border-r border-border bg-card/30 p-4">
          <VikramHUD />
        </div>

        {/* Main Content Area - Tabbed Interface */}
        <div className="flex-1 p-6">
          <Tabs defaultValue="quests" className="h-full">
            <TabsList className="grid w-full grid-cols-4 mb-6">
              <TabsTrigger value="quests" className="flex items-center space-x-2">
                <ScrollText className="w-4 h-4" />
                <span>Quests</span>
              </TabsTrigger>
              <TabsTrigger value="harem" className="flex items-center space-x-2">
                <Users className="w-4 h-4" />
                <span>Harem</span>
              </TabsTrigger>
              <TabsTrigger value="shop" className="flex items-center space-x-2">
                <ShoppingCart className="w-4 h-4" />
                <span>Shop</span>
              </TabsTrigger>
              <TabsTrigger value="log" className="flex items-center space-x-2">
                <Activity className="w-4 h-4" />
                <span>Event Log</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="quests" className="h-full">
              <QuestLog />
            </TabsContent>

            <TabsContent value="harem" className="h-full">
              <HaremManagement />
            </TabsContent>

            <TabsContent value="shop" className="h-full">
              <SystemShop />
            </TabsContent>

            <TabsContent value="log" className="h-full">
              <EventLog />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
}

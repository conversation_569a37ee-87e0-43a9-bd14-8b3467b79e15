'use client';

import { useState, useEffect, useCallback } from 'react';
import { SystemState, VikramProfile, Quest, LogEntry, GameSettings, HaremMember, SystemItem } from '@/types/game';

const initialSettings: GameSettings = {
  wealthConversionRate: 100000, // ₹1,00,000 = 1 SP
  notificationVerbosity: 'Full',
  theme: 'Chimera Purple',
  selectedModel: 'gemini-2.0-flash-exp',
  apiKey: '',
  autoSave: true,
  explicitContent: true,
};

const createInitialProfile = (): VikramProfile => ({
  name: "<PERSON><PERSON><PERSON>",
  title: "System Initiate",
  level: 1,
  
  // Core Resources
  stamina: 100,
  maxStamina: 100,
  lust: 50,
  maxLust: 100,

  // Core Attributes
  dominance: 10,
  manipulation: 8,
  deception: 12,

  // System Currencies & Points
  corruption: 15,
  alphaPoints: 0,
  deviantXP: 0,
  systemPoints: 250,
  wealth: 500000, // ₹5 lakh starting wealth

  // Unlocked Abilities
  skills: [],
  traits: [],

  // The Harem
  harem: [
    {
      id: 'priya_singh',
      name: '<PERSON><PERSON>',
      designation: 'Mother',
      corruptionStage: 2,
      submissionLevel: 35,
      loyalty: 80,
      lustMeter: 25,
      specialSkills: ['Financial Access', 'Household Management'],
      status: 'Asset',
      profileImage: '/images/priya.jpg'
    },
    {
      id: 'ananya_singh',
      name: 'Ananya Singh',
      designation: 'Sister',
      corruptionStage: 1,
      submissionLevel: 20,
      loyalty: 60,
      lustMeter: 15,
      specialSkills: ['College Network', 'Social Media'],
      status: 'Pawn',
      profileImage: '/images/ananya.jpg'
    }
  ],
  
  // Inventory
  inventory: [],
});

const createInitialQuests = (): Quest[] => [
  {
    id: 'family_dynamics',
    title: 'Family Dynamics',
    type: 'Main',
    target: 'Priya Singh',
    objective: 'Establish dominance within the household and increase mother\'s submission',
    status: 'Active',
    requirements: {
      corruption: 10,
    },
    rewards: {
      systemPoints: 100,
      deviantXP: 50,
      alphaPoints: 25,
      haremUpdate: {
        targetId: 'priya_singh',
        submissionChange: 15,
        corruptionStageUp: true
      }
    }
  },
  {
    id: 'college_network',
    title: 'College Network',
    type: 'Side',
    target: 'College Classmates',
    objective: 'Build influence among college peers and identify potential targets',
    status: 'Active',
    requirements: {
      dominance: 15,
    },
    rewards: {
      systemPoints: 75,
      deviantXP: 25,
      newSkill: 'Social Manipulation Lvl 1'
    }
  }
];

const createInitialEventLog = (): LogEntry[] => [
  {
    id: 'system_init',
    timestamp: new Date(),
    content: 'Vikram Singh System OS initialized successfully',
    type: 'System'
  },
  {
    id: 'inheritance_received',
    timestamp: new Date(Date.now() - 60000),
    content: 'Inheritance of ₹5,00,000 received and processed',
    type: 'System'
  },
  {
    id: 'quest_family_available',
    timestamp: new Date(Date.now() - 120000),
    content: 'New main quest available: Family Dynamics',
    type: 'Quest'
  }
];

export function useSystemState() {
  const [systemState, setSystemState] = useState<SystemState>({
    profile: createInitialProfile(),
    activeQuests: createInitialQuests(),
    eventLog: createInitialEventLog(),
    gameSettings: initialSettings,
    isLoading: false,
  });

  // Load saved state on mount
  useEffect(() => {
    const savedState = localStorage.getItem('vikram_system_state');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        // Convert timestamp strings back to Date objects
        if (parsed.eventLog) {
          parsed.eventLog = parsed.eventLog.map((entry: any) => ({
            ...entry,
            timestamp: new Date(entry.timestamp)
          }));
        }
        setSystemState(prevState => ({
          ...prevState,
          ...parsed,
          isLoading: false,
        }));
      } catch (error) {
        console.error('Failed to load saved state:', error);
      }
    }
  }, []);

  // Auto-save when state changes
  useEffect(() => {
    if (systemState.gameSettings.autoSave) {
      localStorage.setItem('vikram_system_state', JSON.stringify(systemState));
    }
  }, [systemState]);

  const updateProfile = useCallback((updates: Partial<VikramProfile>) => {
    setSystemState(prevState => ({
      ...prevState,
      profile: { ...prevState.profile, ...updates },
    }));
  }, []);

  const addLogEntry = useCallback((entry: Omit<LogEntry, 'id' | 'timestamp'>) => {
    const newEntry: LogEntry = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...entry
    };
    
    setSystemState(prevState => ({
      ...prevState,
      eventLog: [...prevState.eventLog, newEntry],
    }));
  }, []);

  const updateQuest = useCallback((questId: string, updates: Partial<Quest>) => {
    setSystemState(prevState => ({
      ...prevState,
      activeQuests: prevState.activeQuests.map(quest =>
        quest.id === questId ? { ...quest, ...updates } : quest
      ),
    }));
  }, []);

  const completeQuest = useCallback((questId: string) => {
    const quest = systemState.activeQuests.find(q => q.id === questId);
    if (!quest) return;

    // Apply rewards
    const rewards = quest.rewards;
    const profileUpdates: Partial<VikramProfile> = {};

    if (rewards.systemPoints) {
      profileUpdates.systemPoints = systemState.profile.systemPoints + rewards.systemPoints;
    }
    if (rewards.deviantXP) {
      profileUpdates.deviantXP = systemState.profile.deviantXP + rewards.deviantXP;
    }
    if (rewards.alphaPoints) {
      profileUpdates.alphaPoints = systemState.profile.alphaPoints + rewards.alphaPoints;
    }
    if (rewards.newSkill) {
      profileUpdates.skills = [...systemState.profile.skills, rewards.newSkill];
    }
    if (rewards.newTitle) {
      profileUpdates.title = rewards.newTitle;
    }

    // Update harem member if specified
    if (rewards.haremUpdate) {
      const haremUpdate = rewards.haremUpdate;
      profileUpdates.harem = systemState.profile.harem.map(member => {
        if (member.id === haremUpdate.targetId) {
          const updatedMember = { ...member };
          if (haremUpdate.submissionChange) {
            updatedMember.submissionLevel = Math.min(100, Math.max(0, 
              member.submissionLevel + haremUpdate.submissionChange
            ));
          }
          if (haremUpdate.loyaltyChange) {
            updatedMember.loyalty = Math.min(100, Math.max(0, 
              member.loyalty + haremUpdate.loyaltyChange
            ));
          }
          if (haremUpdate.corruptionStageUp && member.corruptionStage < 5) {
            updatedMember.corruptionStage = (member.corruptionStage + 1) as 1 | 2 | 3 | 4 | 5;
          }
          return updatedMember;
        }
        return member;
      });
    }

    updateProfile(profileUpdates);
    updateQuest(questId, { status: 'Completed' });
    addLogEntry({
      content: `Quest completed: ${quest.title}`,
      type: 'Quest'
    });

    // Add reward log entries
    if (rewards.systemPoints) {
      addLogEntry({
        content: `Earned ${rewards.systemPoints} System Points`,
        type: 'Reward'
      });
    }
  }, [systemState, updateProfile, updateQuest, addLogEntry]);

  const convertWealth = useCallback((inrAmount: number) => {
    if (inrAmount > systemState.profile.wealth) return;

    const spGained = Math.floor(inrAmount / systemState.gameSettings.wealthConversionRate);
    
    updateProfile({
      wealth: systemState.profile.wealth - inrAmount,
      systemPoints: systemState.profile.systemPoints + spGained
    });

    addLogEntry({
      content: `Converted ₹${inrAmount.toLocaleString()} to ${spGained} SP`,
      type: 'System'
    });
  }, [systemState, updateProfile, addLogEntry]);

  const purchaseItem = useCallback((item: SystemItem, price: number) => {
    if (price > systemState.profile.systemPoints) return;

    const existingItem = systemState.profile.inventory.find(i => i.id === item.id);
    
    if (existingItem) {
      updateProfile({
        systemPoints: systemState.profile.systemPoints - price,
        inventory: systemState.profile.inventory.map(i =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        )
      });
    } else {
      updateProfile({
        systemPoints: systemState.profile.systemPoints - price,
        inventory: [...systemState.profile.inventory, { ...item, quantity: 1 }]
      });
    }

    addLogEntry({
      content: `Purchased ${item.name} for ${price} SP`,
      type: 'System'
    });
  }, [systemState, updateProfile, addLogEntry]);

  const updateSettings = useCallback((updates: Partial<GameSettings>) => {
    setSystemState(prevState => ({
      ...prevState,
      gameSettings: { ...prevState.gameSettings, ...updates },
    }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    setSystemState(prevState => ({
      ...prevState,
      isLoading,
    }));
  }, []);

  const resetSystem = useCallback(() => {
    setSystemState({
      profile: createInitialProfile(),
      activeQuests: createInitialQuests(),
      eventLog: createInitialEventLog(),
      gameSettings: systemState.gameSettings, // Keep settings
      isLoading: false,
    });
  }, [systemState.gameSettings]);

  const saveSystem = useCallback(() => {
    localStorage.setItem('vikram_system_state', JSON.stringify(systemState));
    addLogEntry({
      content: 'System state saved successfully',
      type: 'System'
    });
  }, [systemState, addLogEntry]);

  return {
    systemState,
    updateProfile,
    addLogEntry,
    updateQuest,
    completeQuest,
    convertWealth,
    purchaseItem,
    updateSettings,
    setLoading,
    resetSystem,
    saveSystem,
  };
}
